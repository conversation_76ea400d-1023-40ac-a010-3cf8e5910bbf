<template>
    <div
        ref="goodsRemark"
        v-abc-click-outside="closePopper"
        :style="inputWrapperStyle"
        :class="[
            'abc-input-wrapper',
            'hospital__goods-remark-wrapper',
            size ? `abc-input-${ size }-wrapper` : '',
            {
                'is-disabled': disabled,
                'is-focus': isFocus || showPopper,
            },
        ]"
        data-cy="goods-remark"
        @click="clickSelect"
    >
        <div
            class="input-tag"
            :style="{
                'max-width': showIvgtt ? '46px' : 'none', 'padding-left': !hasSupplementTag && !renderTags.length && !psychotropicNarcoticTypeVisible && !hasOperateIng && !hasOperateAfter ? '0px' : '4px'
            }"
        >
            <supplement-icon v-if="hasSupplementTag"></supplement-icon>
            <source-tag
                v-for="item in renderTags"
                :key="item.name"
                :name="item.name"
                :color="item.color"
                style="margin-right: 4px;"
                @click.native="handleClickTag"
            ></source-tag>
            <psychotropic-narcotic-type v-if="psychotropicNarcoticTypeVisible" :tags="tags"></psychotropic-narcotic-type>
        </div>
        <input
            ref="abcinput"
            v-model="currentValue"
            :class="[
                'abc-input__inner',
                { 'text-center': textCenter },
            ]"
            :style="inputStyle"
            :tabindex="tabindex"
            :disabled="disabled"
            :readonly="readonly"
            :maxlength="maxLength"
            :placeholder="placeholder"
            :title="currentValue"
            data-cy="input-goods-remark"
            @blur="handleBlur"
            @focus="handleFocus"
            @keydown.enter="handelEnter"
            @keydown.delete="handleDelete"
            @keydown.tab="closePopper"
            @keydown.down.prevent="down"
            @keydown.up.prevent="up"
            @keydown.left="left"
            @keydown.right="right"
        />
        <medicine-remarks
            v-if="showPopper"
            ref="childComponent"
            :visible.sync="showPopper"
            :current-value="currentValue"
            :placement="placement"
            :select-pharmacy-no="curSelectPharmacyNo"
            :default-pharmacy="defaultPharmacy"
            :show-medicine-remark-options="showMedicineRemarkOptions"
            :show-medicine-source-options="showMedicineSourceOptions"
            :show-chinese-remark-options="showChineseRemarkOptions"
            :tag-types="tags"
            :show-no-charge="showNoCharge"
            :show-psychotropic-narcotic-type="showPsychotropicNarcoticType"
            :charge-flag="curSelectChargeFlag"
        ></medicine-remarks>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import common from 'components/common/form';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import {
        AdviceTagEnum, AdviceTagEnumTEXT,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import SourceTag from 'src/views/layout/source-tag.vue';
    import PsychotropicNarcoticType from 'src/views/layout/psychotropic-narcotic-type';
    import MedicineRemarks from './medicine-remarks.vue';

    import SupplementIcon from '@/views-hospital/medical-prescription/components/supplement-icon.vue';

    import { resetStockByPharmacyNo } from 'views/layout/prescription/utils.js';
    import { PharmacyTypeEnum } from '@abc/constants';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import clone from 'utils/clone.js';
    import $style from 'styles/theme.module.scss';
    import { PsychotropicNarcoticTypeEnum } from 'views/outpatient/constants.js';
    export default {
        name: 'AbcGroupSelect',
        components: {
            MedicineRemarks,
            SourceTag,
            SupplementIcon,
            PsychotropicNarcoticType,
        },
        directives: {
            focus: {
                // 指令的定义
                inserted(el) {
                    el.addEventListener('click', () => {
                        $(el).prev()[0].focus();
                    });
                },
            },
        },
        mixins: [common],
        props: {
            value: [String, Number],
            placeholder: String,
            tabindex: [Number, String],
            width: Number,
            disabled: Boolean,
            readonly: {
                type: Boolean,
                default: true,
            },
            index: Number,
            placement: {
                type: String,
                default: 'bottom-end',
            },
            size: String,
            maxLength: {
                type: [Number, String],
                default: 30,
            },

            // 聚焦自动展开options
            focusShowOptions: {
                type: Boolean,
                default: false,
            },
            textCenter: {
                type: Boolean,
                default: false,
            },
            inputStyle: [Object, String],

            prFormItem: {
                type: Object,
            },

            // 是开单来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },

            // 支持标签展示
            supportTag: {
                type: Boolean,
                default: false,
            },

            showMedicineRemarkOptions: {
                type: Boolean,
                default: false,
            },
            showMedicineSourceOptions: {
                type: Boolean,
                default: false,
            },
            /**
             * @desc 显示中药的备注列表
             * <AUTHOR>
             * @date 2023-08-31 11:49:13
             */
            showChineseRemarkOptions: {
                type: Boolean,
                default: false,
            },
            showNoCharge: {
                type: Boolean,
                default: false,
            },
            departmentId: String,
            wardAreaId: String,
            tagTypes: Array,
            // 是否展示精麻类选项
            showPsychotropicNarcoticType: {
                type: Boolean,
                default: false,
            },
            showIvgtt: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                showPopper: false,
                isFocus: false,
                hoverPIndex: null,
                defaultPharmacy: {
                    no: undefined,
                    name: undefined,
                    type: undefined,
                },
                showSupplementaryTag: false,
                showUrgentTag: false,
                isNeedConfirm: false,
                tags: [],
                AdviceTagEnum,
                PsychotropicNarcoticTypeEnum,
            };
        },
        computed: {
            ...mapGetters([
                'enableLocalPharmacyList',
                'pharmacyRuleList',
                'multiPharmacyCanUse',
            ]),
            // 修改tags计算属性，合并group级别和医嘱级别的tags
            tags() {
                const groupTags = this.tagTypes || [];
                const adviceTags = this.prFormItem?.adviceRule?.tagTypes || [];

                // 过滤掉精麻类tag，这些现在由单个医嘱维护
                const filteredGroupTags = groupTags.filter(tag =>
                    ![AdviceTagEnum.JING_1, AdviceTagEnum.JING_2, AdviceTagEnum.DU, AdviceTagEnum.MA_ZUI].includes(tag)
                );

                return [...filteredGroupTags, ...adviceTags];
            },

            // 更新psychotropicNarcoticTypeVisible计算属性
            psychotropicNarcoticTypeVisible() {
                if (!this.showPsychotropicNarcoticType) return false;

                const adviceTags = this.prFormItem?.adviceRule?.tagTypes || [];
                return adviceTags.some(tag =>
                    [AdviceTagEnum.JING_1, AdviceTagEnum.JING_2, AdviceTagEnum.DU, AdviceTagEnum.MA_ZUI, AdviceTagEnum.OPERATE_ING, AdviceTagEnum.OPERATE_AFTER].includes(tag)
                );
            }
            watchData() {
                return {
                    departmentId: this.departmentId,
                    wardAreaId: this.wardAreaId,
                };
            },

            curSelectPharmacyNo() {
                return this.prFormItem?.pharmacyNo || undefined;
            },

            curSelectChargeFlag() {
                return this.prFormItem?.chargeFlag || OutpatientChargeTypeEnum.DEFAULT;
            },

            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            inputWrapperStyle() {
                let width = '';
                if (typeof this.width === 'number') {
                    width = `${this.width}px`;
                } else {
                    width = this.width;
                }
                return {
                    width,
                };
            },
            tagName() {
                const {
                    chargeFlag,
                    pharmacyType,
                    pharmacyNo,
                } = this.prFormItem || {};

                if (chargeFlag === OutpatientChargeTypeEnum.NO_CHARGE) {
                    return '自备';
                }

                if (!this.multiPharmacyCanUse) return '';
                if (this.defaultPharmacy.no === undefined) return '';
                if (pharmacyType !== PharmacyTypeEnum.LOCAL_PHARMACY) {
                    return '';
                }
                if (this.defaultPharmacy.no !== pharmacyNo) {
                    const res = this.enableLocalPharmacyList.find((it) => it.no === pharmacyNo);
                    if (this.showIvgtt) {
                        return res?.name.slice(0, 1) || '';
                    }
                    return res?.name.slice(0, 3) || '';
                }
                return '';
            },

            showInputTag() {
                return this.supportTag && this.tagName;
            },
            hasSupplementTag() {
                return this.tags.includes(AdviceTagEnum.SUPPLEMENT);
            },
            // 术中
            hasOperateIng() {
                return this.tags.includes(AdviceTagEnum.OPERATE_ING);
            },
            // 术后
            hasOperateAfter() {
                return this.tags.includes(AdviceTagEnum.OPERATE_AFTER);
            },
            renderTags() {
                const res = [];
                // if (this.showInputTag) {
                //     res.push({
                //         name: this.tagName,
                //     });
                // }
                this.tags.forEach((item) => {
                    if (item === AdviceTagEnum.URGENT) {
                        res.push({
                            name: AdviceTagEnumTEXT[AdviceTagEnum.URGENT],
                            color: $style.R1,
                        });
                    }
                });
                return res;
            },
            calcPaddingLeftStyle() {
                const len = this.tags?.length || 0;
                let left = 0;
                // if (this.showInputTag) {
                //     left += 34;
                // }
                if (len) {
                    left += (22 * len);
                }
                if (this.showIvgtt && left > 42) {
                    left = 42;
                }
                return left;
            },
        },

        watch: {
            watchData: {
                handler(val) {
                    this.initDefaultPharmacy(val);
                },
                immediate: true,
            },
            tagTypes: {
                handler(val) {
                    this.tags = val?.length ? clone(val) : [];
                },
                immediate: true,
            },
        },
        mounted() {

        },
        created() {
            this.$on('handleOptionClick', this.handleOptionSelect);
            this.$on('handleSourceClick', this.handleSourceClick);
            this.$on('handleTagTypeClick', this.handleTagTypeClick);
            this.$on('handlePsychotropicNarcoticTagClick', this.handlePsychotropicNarcoticTagClick);
        },
        beforeDestroy() {
            this.$off('handleOptionClick', this.handleOptionSelect);
            this.$off('handleSourceClick', this.handleSourceClick);
            this.$off('handleTagTypeClick', this.handleTagTypeClick);
        },
        methods: {
            setTags(tag) {
                let isNeedConfirm = false;
                if (this.tags.includes(tag)) {
                    this.tags = this.tags.filter((item) => {return item !== tag;});
                    if (tag === this.AdviceTagEnum.SUPPLEMENT) {
                        this.$Toast({
                            message: '已取消补开',
                            type: 'info',
                        });
                    }
                    if (tag === this.AdviceTagEnum.URGENT) {
                        this.$Toast({
                            message: '已取消加急',
                            type: 'info',
                        });
                    }
                } else {
                    // 已经有这几个标记了需要先删掉再增加
                    if ([AdviceTagEnum.JING_1, AdviceTagEnum.JING_2, AdviceTagEnum.DU, AdviceTagEnum.MA_ZUI].includes(tag) && this.tags.find((item) => {
                        return [AdviceTagEnum.JING_1, AdviceTagEnum.JING_2, AdviceTagEnum.DU, AdviceTagEnum.MA_ZUI].includes(item);
                    })) {
                        this.tags = this.tags.filter((item) => {return ![AdviceTagEnum.JING_1, AdviceTagEnum.JING_2, AdviceTagEnum.DU, AdviceTagEnum.MA_ZUI].includes(item);});
                    }
                    // 术中和术后是互斥的, 已经选了其中一个, 再选另一个就会先取消前一个
                    if (tag === AdviceTagEnum.OPERATE_ING && this.tags.includes(AdviceTagEnum.OPERATE_AFTER)) {
                        this.tags = this.tags.filter((it) => it !== AdviceTagEnum.OPERATE_AFTER);
                    }
                    if (tag === AdviceTagEnum.OPERATE_AFTER && this.tags.includes(AdviceTagEnum.OPERATE_ING)) {
                        this.tags = this.tags.filter((it) => it !== AdviceTagEnum.OPERATE_ING);
                    }
                    this.tags.push(tag);

                    if (tag === this.AdviceTagEnum.SUPPLEMENT) {
                        isNeedConfirm = true;
                    }
                }
                this.showPopper = false;
                this.$emit('change-tags', this.tags, isNeedConfirm);
            },
            initDefaultPharmacy(options) {
                const {
                    departmentId,
                    wardAreaId,
                } = options;
                this.defaultPharmacy = getDefaultPharmacy(this.pharmacyRuleList, {
                    departmentId,
                    wardAreaId,
                    goodsInfo: this.prFormItem?.productInfo,
                });
            },
            handleClickTag() {
                this.$nextTick(() => {
                    this.$refs.abcinput.focus();
                    this.$refs.abcinput.selectionStart = 0;
                    this.$refs.abcinput.selectionEnd = this.$refs.abcinput.value.length;
                });
            },
            up(e) {
                if (this.readonly) {
                    e.preventDefault();
                }

                if (!this.showPopper) {
                    this.$emit('up', e);
                    return;
                }
                e.stopPropagation();
                this.$refs.childComponent?.up();
            },

            down(e) {
                if (this.readonly) {
                    e.preventDefault();
                }

                if (!this.showPopper) {
                    this.$emit('down', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.down();
            },

            left(e) {
                if (this.readonly) {
                    e.preventDefault();
                }
                // 当下拉框展开的时候不能左右键切走
                if (!this.showPopper) {
                    this.$emit('left', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.left();

            },

            right(e) {
                if (this.readonly) {
                    e.preventDefault();
                }
                // 当下拉框展开的时候不能左右键切走
                if (!this.showPopper) {
                    this.$emit('right', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.right();
            },

            handleFocus(event) {
                this.isFocus = true;
                this.$emit('focus', event);
                if (this.focusShowOptions && !this.disabled) {
                    this._isFocusShow = true;
                    this.showPopper = true;
                }
            },

            handleBlur(event) {
                this.isFocus = false;
                this.$emit('blur', event);
            },
            handleDelete(event) {
                this.isFocus = true;
                this.$emit('delete', event);
            },

            clickSelect() {
                if (this.disabled) return false;
                /**
                 * @desc 解决focus click冲突问题
                 * 用户点击 会先触发focus事件 后执行click事件
                 * 开启了 focusShowOptions focus事件就已经展开options，后执行的click事件不再响应
                 * <AUTHOR>
                 * @date 2022-01-26 17:35:58
                 */
                if (this._isFocusShow) {
                    this._isFocusShow = false;
                    return false;
                }
                this.showPopper = !this.showPopper;
            },

            handelEnter(event) {
                if (this.showPopper) {
                    this.$refs.childComponent?.enter();
                    this.$emit('enter', event);
                    this.showPopper = false;
                } else {
                    this.showPopper = !this.showPopper;
                }
            },

            closePopper() {
                this.showPopper = false;
            },

            handleOptionSelect(val) {
                val !== this.value && this.$emit('change', val, this.index);
                this.formItem && this.formItem.$emit('formFieldChange', val);
                this.currentValue = val;
                this.showPopper = false;
            },

            /**
             * @desc 药品来源选择
             * <AUTHOR>
             * @date 2022-10-14 11:55:15
             */
            handleSourceClick(val) {
                if (val.id === null && this.showNoCharge) {
                    this.$set(this.prFormItem, 'chargeFlag', OutpatientChargeTypeEnum.NO_CHARGE);
                    this.prFormItem.pharmacyType = '';
                    this.prFormItem.pharmacyNo = '';
                    this.prFormItem.pharmacyName = '';
                    this.$nextTick(() => {
                        this.$Toast({
                            message: '备注选择【自备】，该药品将不会纳入划价收费',
                            duration: 1500,
                            referenceEl: this.$refs.goodsRemark,
                        });
                    });
                } else {
                    this.prFormItem.pharmacyType = val.type;
                    this.prFormItem.pharmacyNo = val.no;
                    this.prFormItem.pharmacyName = val.name;
                    this.prFormItem.chargeFlag = OutpatientChargeTypeEnum.DEFAULT;
                }
                resetStockByPharmacyNo(this.prFormItem);
                this.$emit('changeExtend', val);
                this.showPopper = false;
            },
            handleTagTypeClick(val) {
                this.setTags(val);
            },
            handlePsychotropicNarcoticTagClick(tagValue) {
                // 获取当前医嘱的tagTypes，如果不存在则初始化为空数组
                if (!this.prFormItem.adviceRule.tagTypes) {
                    this.$set(this.prFormItem.adviceRule, 'tagTypes', []);
                }

                const currentTags = this.prFormItem.adviceRule.tagTypes;
                const tagIndex = currentTags.indexOf(tagValue);

                if (tagIndex > -1) {
                    // 如果已存在，则移除
                    currentTags.splice(tagIndex, 1);
                } else {
                    // 如果不存在，则添加
                    currentTags.push(tagValue);
                }

                // 触发更新
                this.$emit('psychotropic-narcotic-tag-change', {
                    adviceRule: this.prFormItem.adviceRule,
                    tagValue,
                    isAdd: tagIndex === -1,
                });
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.hospital__goods-remark-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;

    .abc-input__inner {
        flex: 1;
        min-width: 47px;
        height: 28px;
        padding: 3px 4px;
        line-height: 1;
        text-align: left;
        cursor: pointer;

        &.text-center {
            padding: 3px 0;
            text-align: center;
        }

        //&.with-tag {
        //    padding-left: 44px !important;
        //}
    }

    .input-tag {
        display: flex;
        align-items: center;
        width: auto;
        overflow: hidden;
    }
}
</style>

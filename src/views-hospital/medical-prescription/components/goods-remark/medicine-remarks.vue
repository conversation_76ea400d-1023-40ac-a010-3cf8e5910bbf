<template>
    <div v-show="hasOptions" ref="suggestions" class="medicine-remark-suggestion-wrapper">
        <ul>
            <abc-space v-if="displayTagOption.length" class="option-tag-title">
                <abc-tag-v2
                    v-for="(tagItem, tagIndex) in displayTagOption"
                    :key="`tag${tagIndex}`"
                    shape="square"
                    size="small"
                    variant="outline"
                    gap="small"
                    style="cursor: pointer;"
                    :theme="(tagTypes || []).includes(tagItem.tagValue) ? 'primary' : 'default'"
                    @click="selectItem(tagItem)"
                >
                    {{ tagItem.content }}
                </abc-tag-v2>
            </abc-space>
            <template v-for="(item, index) in displayOptions">
                <div v-if="item.isTitle" :key="`title${ index}`" class="option-title">
                    <span>{{ item.content }}</span>
                    <abc-icon
                        v-if="item.withSet"
                        icon="set"
                        :size="14"
                        @click="handleClickSet"
                    ></abc-icon>
                </div>
                <div
                    v-else
                    :key="index"
                    class="option-item"
                    :title="item.content"
                    :class="[
                        'ellipsis',
                        { 'is-selected': currentValue && currentValue === item.content },
                        { 'is-hover': hoverIndex === index },
                        { 'is-disabled': !item.content },
                    ]"
                    @mousedown="selectItem(item)"
                >
                    {{ item.content }}
                    <template v-if="item.sourceType === 1">
                        <div v-if="item.id !== null && item.no === defaultPharmacy.no" class="default-tag">
                            默认
                        </div>
                        <abc-icon
                            v-if="item.no === selectPharmacyNo"
                            :size="12"
                            icon="positive_"
                            :color="themeStyle.theme2"
                        ></abc-icon>
                        <abc-icon
                            v-if="chargeFlag && item.id === null"
                            :size="12"
                            icon="positive_"
                            :color="themeStyle.theme2"
                        ></abc-icon>
                    </template>
                    <abc-icon
                        v-if="(hasSupplementaryTag && item.tagValue === AdviceTagEnum.SUPPLEMENT) ||
                            (hasUrgentTag && item.tagValue === AdviceTagEnum.URGENT) ||
                            (hasJin1Tag && item.tagValue === AdviceTagEnum.JING_1) ||
                            (hasJin2Tag && item.tagValue === AdviceTagEnum.JING_2) ||
                            (hasMaZuiTag && item.tagValue === AdviceTagEnum.MA_ZUI) ||
                            (hasDuTag && item.tagValue === AdviceTagEnum.DU)"
                        :size="12"
                        icon="positive_"
                        :color="themeStyle.theme2"
                    ></abc-icon>
                </div>
            </template>
        </ul>
    </div>
</template>

<script type="text/ecmascript-6">
    import DoctorTemplateAPI from 'api/doctor-template';

    import Popper from 'utils/vue-popper';
    import { mapGetters } from 'vuex';
    import ListManageDialog from 'views/layout/list-manage-dialog/index.js';
    import themeStyle from '@/styles/theme.module.scss';
    import {
        AdviceTagEnum,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    export default {
        name: 'AbcMedicineRemarkSelect',
        mixins: [
            Popper,
        ],
        props: {
            visible: Boolean,
            currentValue: String,
            showMedicineRemarkOptions: Boolean,
            showMedicineSourceOptions: Boolean,
            showChineseRemarkOptions: Boolean,
            showNoCharge: Boolean,
            defaultPharmacy: {
                type: Object,
                default: () => {},
            },
            selectPharmacyNo: {
                type: [Number,String],
                default: null,
            },
            tagTypes: Array,
            chargeFlag: Number,
            // 是否展示精麻类选项
            showPsychotropicNarcoticType: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                AdviceTagEnum,
                themeStyle,
                hoverIndex: null,
                zsIndex: 0,
                qtIndex: 0,
            };
        },
        computed: {
            ...mapGetters([
                'westernPRRemarks',
                'enableLocalPharmacyList',
                'multiPharmacyCanUse',
                'hospitalClinicConfig',
                'chineseMedicineConfig',
            ]),
            GroupSelect() {
                let parent = this.$parent;
                while (parent && parent.$options && parent.$options.name !== 'AbcGroupSelect') {
                    parent = parent.$parent;
                }
                return parent;
            },

            hasOptions() {
                return this.displayOptions.length || this.displayTagOption.length;
            },

            remarkOptions() {
                if (!this.showMedicineRemarkOptions) return [];
                if (this.showChineseRemarkOptions) {
                    return this.chineseMedicineConfig.requirement.map((item) => {
                        return {
                            content: item.name,
                            sourceType: 0,
                        };
                    });
                }
                return this.westernPRRemarks.map((item) => {
                    return {
                        ...item,
                        sourceType: 0,
                    };
                });
            },
            psychotropicNarcoticOptions() {
                return [
                    {
                        tagValue: AdviceTagEnum.MA_ZUI,
                        content: '麻醉',
                    },
                    {
                        tagValue: AdviceTagEnum.JING_1,
                        content: '精一',
                    },
                    {
                        tagValue: AdviceTagEnum.JING_2,
                        content: '精二',
                    },
                    {
                        tagValue: AdviceTagEnum.DU,
                        content: '毒',
                    },
                ];
            },

            sourceOptions() {
                let _arr = [];
                if (this.showNoCharge) {
                    _arr.push(
                        {
                            id: null,
                            content: '自备',
                            sourceType: 1,
                        },
                    );
                }
                if (!this.showMedicineSourceOptions) return _arr;
                if (!this.multiPharmacyCanUse) return _arr;
                _arr = _arr.concat(this.enableLocalPharmacyList.map((it) => {
                    return {
                        ...it,
                        content: it.name,
                        sourceType: 1,
                    };
                }));
                return _arr;
            },

            displayTagOption() {
                let _arr = [];
                // 如果支持开补录医嘱
                if (this.hospitalClinicConfig?.createAdvice?.beforeToday) {
                    _arr = _arr.concat([
                        {
                            content: '补开',
                            tagValue: AdviceTagEnum.SUPPLEMENT,
                        },
                    ]);
                }
                _arr = _arr.concat([
                    {
                        content: '加急',
                        tagValue: AdviceTagEnum.URGENT,
                    },
                ]);
                _arr = _arr.concat([
                    {
                        content: '术中',
                        tagValue: AdviceTagEnum.OPERATE_ING,
                    },
                ]);
                _arr = _arr.concat([
                    {
                        content: '术后',
                        tagValue: AdviceTagEnum.OPERATE_AFTER,
                    },
                ]);
                return _arr;
            },

            displayOptions() {
                let _arr = [];
                if (this.remarkOptions.length) {
                    _arr.push({
                        isTitle: true,
                        content: '药品备注',
                        withSet: !this.showChineseRemarkOptions,
                    });
                    _arr = _arr.concat(this.remarkOptions);
                }
                if (this.showPsychotropicNarcoticType) {
                    _arr.push({
                        isTitle: true,
                        content: '标记',
                    });
                    _arr = _arr.concat(this.psychotropicNarcoticOptions);
                }
                // if (this.sourceOptions.length) {
                //     _arr.push({
                //         isTitle: true,
                //         content: '药品来源',
                //     });
                //     _arr = _arr.concat(this.sourceOptions);
                // }
                return _arr;
            },
            hasSupplementaryTag() {
                return this.tagTypes.includes(AdviceTagEnum.SUPPLEMENT);
            },
            hasUrgentTag() {
                return this.tagTypes.includes(AdviceTagEnum.URGENT);
            },
            hasJin1Tag() {
                return this.tagTypes.includes(AdviceTagEnum.JING_1);
            },
            hasJin2Tag() {
                return this.tagTypes.includes(AdviceTagEnum.JING_2);
            },
            hasMaZuiTag() {
                return this.tagTypes.includes(AdviceTagEnum.MA_ZUI);
            },
            hasDuTag() {
                return this.tagTypes.includes(AdviceTagEnum.DU);
            },
        },
        watch: {
            visible: {
                immediate: true,
                handler(val) {
                    this.showPopper = val;
                },
            },
            currentValue: {
                immediate: true,
                handler(val) {
                    this.remarkOptions.forEach((item, index) => {
                        if (val && val === item.content) {
                            this.hoverIndex = index;
                        }
                    });
                },
            },
            hasOptions: {
                handler(val) {
                    if (!val) {
                        this.$emit('visible', false);
                        this.showPopper = false;
                    }
                },
                immediate: true,
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.suggestions;
            const $abcInput = this.$parent.$refs.abcinput;
            const { $el } = $abcInput;
            this.referenceElm = $el || $abcInput;
        },

        beforeDestroy() {
            this.doDestroy();
        },
        methods: {
            selectItem(val) {
                if (val.sourceType === 1) {
                    this.GroupSelect.$emit('handleSourceClick', val);
                    return;
                }
                if (val.tagValue === AdviceTagEnum.SUPPLEMENT) {
                    // 补录医嘱
                    this.GroupSelect.$emit('handleTagTypeClick', AdviceTagEnum.SUPPLEMENT);
                    return;
                }
                if (val.tagValue === AdviceTagEnum.URGENT) {
                    // 加急医嘱
                    this.GroupSelect.$emit('handleTagTypeClick', AdviceTagEnum.URGENT);
                    return;
                }
                if ([AdviceTagEnum.JING_1, AdviceTagEnum.JING_2, AdviceTagEnum.DU, AdviceTagEnum.MA_ZUI].includes(val.tagValue)) {
                    this.GroupSelect.$emit('handleTagTypeClick', val.tagValue);
                    return;
                }
                // 术中
                if (val.tagValue === AdviceTagEnum.OPERATE_ING) {
                    this.GroupSelect.$emit('handleTagTypeClick', AdviceTagEnum.OPERATE_ING);
                    return;
                }
                // 术后
                if (val.tagValue === AdviceTagEnum.OPERATE_AFTER) {
                    this.GroupSelect.$emit('handleTagTypeClick', AdviceTagEnum.OPERATE_AFTER);
                    return;
                }
                this.GroupSelect.$emit('handleOptionClick', val.content);
            },

            /**
             * @desc 父组件按 下
             * <AUTHOR>
             * @date 2018/08/06 13:59:50
             */
            down() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                this.hoverIndex += 1;
                if (this.displayOptions[this.hoverIndex]?.isTitle) {
                    this.hoverIndex += 1;
                }
                if (this.hoverIndex > this.displayOptions.length - 1) {
                    this.hoverIndex = this.displayOptions.length - 1;
                }
            },

            /**
             * @desc 父组件按 上
             * <AUTHOR>
             * @date 2018/08/06 14:00:30
             */
            up() {
                this.hoverIndex--;
                if (this.displayOptions[this.hoverIndex]?.isTitle) {
                    this.hoverIndex--;
                }
                if (this.hoverIndex < 0) {
                    this.hoverIndex = 0;
                }
            },

            /**
             * @desc 父组件按 左
             * <AUTHOR>
             * @date 2018/08/06 14:00:54
             */
            left() {

            },

            /**
             * @desc 父组件按 右
             * <AUTHOR>
             * @date 2018/08/06 14:01:12
             */
            right() {

            },

            /**
             * @desc 父组件按 回车
             * <AUTHOR>
             * @date 2018/08/06 14:01:35
             */
            enter() {
                if (this.hoverIndex === null) return false;
                this.selectItem(this.displayOptions[ this.hoverIndex ]);
            },

            handleClickSet() {
                this.$emit('update:visible', false);
                this._listManageDialog = new ListManageDialog({
                    title: '编辑备注',
                    list: this.remarkOptions,
                    contentStyle: {
                        width: '390px',
                        height: '300px',
                    },
                    singleRowMaxLength: 15,
                    onSubmit: (list, deleteList) => {
                        this.submitHandler(list, deleteList);
                    },
                }).generateDialog();
            },

            async submitHandler(list, deleteList) {
                try {
                    await DoctorTemplateAPI.updateDoctorTemplates({
                        sceneType: 1,
                        updateTemplateItemList: list.filter((item) => {
                            return item.id || item.content;
                        }).concat(deleteList),
                    });
                    await this.$store.dispatch('fetchDoctorWesternPRRemarks');
                    this._listManageDialog.close();
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (err) {
                    console.error(err);
                }
            },
        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';
    @import 'src/styles/mixin.scss';

    .medicine-remark-suggestion-wrapper {
        position: absolute;
        top: 24px;
        left: 0;
        z-index: 9999;
        box-sizing: border-box;
        width: 228px;
        max-height: 220px;
        padding: 4px 0;
        margin-top: 2px;
        overflow-x: hidden;
        overflow-y: overlay;
        font-size: 0;
        background-color: #ffffff;
        border: 1px solid $P3;
        border-radius: var(--abc-border-radius-small);
        box-shadow: $boxShadowContent;

        @include scrollBar(true);

        .option-tag-title {
            height: 30px;
            padding: 0 2px 0 8px;
        }

        .option-title {
            display: flex;
            align-items: center;
            height: 30px !important;
            padding: 0 2px 0 8px;
            cursor: default;

            > span {
                font-size: 12px;
                color: $T2;
            }

            .abc-icon {
                margin-left: auto;
                color: $T2;
                cursor: pointer;

                &:hover {
                    color: $B2;
                }
            }
        }

        .option-item {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            width: 100%;
            height: 30px;
            padding: 0 12px 0 8px;
            font-size: 12px;
            cursor: pointer;

            .default-tag {
                margin-left: 4px;
                font-size: 12px;
                color: $T2;
            }

            .abc-icon {
                margin-left: auto;
            }

            &:hover {
                /* <!--background-color: $theme4;--> */
                background-color: $P4;
            }

            &.is-hover {
                /* <!--background-color: $theme4;--> */
                background-color: $P4;
            }

            &.is-selected {
                color: #ffffff;

                /* <!--background-color: $theme2;--> */
                background-color: #00ace9;
            }

            &.is-disabled {
                cursor: default;

                &:hover {
                    background-color: #ffffff;
                }
            }

            &.half {
                width: 80px;
            }

            &.last-item {
                border-right: 0;
            }
        }
    }
</style>

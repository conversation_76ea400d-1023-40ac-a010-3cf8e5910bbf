/**
 * 在DataView中写入字符串
 * @param {DataView} view DataView对象
 * @param {number} offset 偏移量
 * @param {string} string 要写入的字符串
 */
export function writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}

/**
 * 将录音数据转换为WAV格式文件
 * @param {Array} audioData 录音数据（Int8Array格式）
 * @returns {Blob} WAV格式的Blob对象
 */
export function createWavFile(audioData) {
    // 采样率16000，单声道，16位
    const sampleRate = 16000;
    const numChannels = 1;
    const bitsPerSample = 16;

    // 创建WAV文件头
    const dataLength = audioData.length;
    const buffer = new ArrayBuffer(44 + dataLength);
    const view = new DataView(buffer);

    // RIFF标识
    writeString(view, 0, 'RIFF');
    // 文件长度
    view.setUint32(4, 36 + dataLength, true);
    // WAVE标识
    writeString(view, 8, 'WAVE');
    // fmt子块标识
    writeString(view, 12, 'fmt ');
    // fmt子块长度
    view.setUint32(16, 16, true);
    // 音频格式（PCM = 1）
    view.setUint16(20, 1, true);
    // 声道数
    view.setUint16(22, numChannels, true);
    // 采样率
    view.setUint32(24, sampleRate, true);
    // 字节率 = 采样率 * 通道数 * 位深 / 8
    view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);
    // 块对齐 = 通道数 * 位深 / 8
    view.setUint16(32, numChannels * bitsPerSample / 8, true);
    // 位深
    view.setUint16(34, bitsPerSample, true);
    // data子块标识
    writeString(view, 36, 'data');
    // data子块长度
    view.setUint32(40, dataLength, true);

    // 写入音频数据
    const dataView = new Uint8Array(buffer, 44);
    for (let i = 0; i < dataLength; i++) {
        dataView[i] = audioData[i];
    }

    return new Blob([buffer], { type: 'audio/wav' });
}

/**
 * 备用方法：手动创建AudioBuffer
 * @param {Array} audioData 原始音频数据
 * @param {AudioContext} audioContext AudioContext实例
 * @returns {AudioBuffer} AudioBuffer实例
 */
function createFallbackAudioBuffer(audioData, audioContext) {
    const sampleRate = 16000;
    const numChannels = 1;
    const audioBuffer = audioContext.createBuffer(numChannels, audioData.length, sampleRate);
    const channelData = audioBuffer.getChannelData(0);

    // 假设数据是16位的PCM，范围为-32768到32767
    for (let i = 0; i < audioData.length; i++) {
        // 将Int8Array转换为-1到10之间的浮点数
        // 注意：这里的转换可能需要根据实际的音频格式调整
        channelData[i] = (audioData[i] < 128) ? audioData[i] / 128.0 : (audioData[i] - 256) / 128.0;
    }

    return audioBuffer;
}

/**
 * 从原始音频数据创建AudioBuffer
 * @param {Array} audioData 原始音频数据
 * @param {AudioContext} audioContext AudioContext实例
 * @returns {Promise<AudioBuffer>} AudioBuffer实例
 */
export function createAudioBufferFromData(audioData, audioContext) {
    // 创建WAV格式的数据
    const wavBlob = createWavFile(audioData);

    // 使用FileReader将Blob转换为ArrayBuffer
    return new Promise((resolve) => {
        const fileReader = new FileReader();
        fileReader.onload = (event) => {
            // 解码音频数据
            audioContext.decodeAudioData(event.target.result, (buffer) => {
                resolve(buffer);
            }, (error) => {
                console.error('音频解码失败:', error);
                // 如果解码失败，尝试手动创建Buffer
                const fallbackBuffer = createFallbackAudioBuffer(audioData, audioContext);
                resolve(fallbackBuffer);
            });
        };
        fileReader.readAsArrayBuffer(wavBlob);
    });
}
function _send(data) {
    try {
        if (window.feLogger) {
            window.feLogger.send(data);
        } else {
            console.log('Logger: ', data);
        }
    } catch (e) {
        console.error('Logger.exception', e);
    }
}

export default class Logger {
    /**
     *  用于数据上报
     * @param scene {string} 场景值，用于区分上报场景
     * @param data {Any} 自定义数据
     */
    static report({
        scene,
        data,
    }) {
        return _send({
            scene,
            data,
        });
    }
}


const guid = () => {
    return 'xxxxxxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

/**
 *
 * @param scene
 * @param context
 * @returns {{report: function(step: string, extraData?: {}): void}}
 */
export const createTraceLogger = (scene, context = {}) => {
    const traceId = guid();
    return {
        traceId,
        context,
        report: (step, extraData = {}) => {
            Logger.report({
                scene,
                data: {
                    traceId,
                    step,
                    ...context,
                    ...extraData,
                },
            });
        },
    };
};

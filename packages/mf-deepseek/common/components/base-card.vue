<template>
    <div class="deepseek-base-card">
        <slot></slot>
        <abc-flex v-if="loaded" style="margin-top: 8px;" justify="flex-end">
            <accept-button @click="handleClick"></accept-button>
        </abc-flex>
    </div>
</template>

<script>
    import { defineComponent } from 'vue';
    import AcceptButton from './accept-button.vue';

    export default defineComponent({
        name: 'BseCard',
        
        components: {
            AcceptButton,
        },
        
        props: {
            loaded: {
                type: Boolean,
                default: false,
            },
        },

        methods: {
            handleClick() {
                this.$emit('click');
            },
        },
    });
</script>

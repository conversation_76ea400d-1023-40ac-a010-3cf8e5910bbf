<template>
    <div>
        <abc-dialog
            v-if="visible"
            v-model="visible"
            title="发药出库"
            class="o2o-goods-out-dialog"
            append-to-body
            disabled-keyboard
            data-cy="o2o-goods-out-dialog"
            responsive
            :auto-focus="false"
        >
            <template #title-append>
                <img
                    v-if="outStockStatus === EcOrderOutStatus.OUT_STOCK"
                    style="position: absolute; top: 12px; left: 150px; z-index: 999999; width: 64px; height: 64px;"
                    src="~assets/images/icon/out-stock.png"
                    alt=""
                />
            </template>

            <template v-if="!disabled" #top-extend>
                <tips-trace-code></tips-trace-code>
            </template>

            <abc-layout v-abc-loading="contentLoading">
                <abc-section>
                    <abc-form
                        ref="form"
                        is-excel
                        item-no-margin
                    >
                        <abc-descriptions
                            :column="4"
                            :label-width="108"
                            label-align="left"
                            :bordered="true"
                            :grid="true"
                            :background="true"
                            size="large"
                            :custom-title-style="{ 'background-color': 'var(--abc-color-LY4)' }"
                        >
                            <template #title>
                                {{ ecTypeText }}#{{ orderDaySeq }} {{ receiverInfoStr }}
                                <abc-tag-v2
                                    v-if="orderDetail.orderTypeFlag & 8"
                                    variant="outline"
                                    shape="square"
                                    size="small"
                                    theme="danger"
                                    style="margin-left: 8px;"
                                >
                                    处方
                                </abc-tag-v2>
                            </template>
                            <abc-descriptions-item label="预计收入金额">
                                <abc-money :value="orderDetail?.estimatedIncomeDetail?.estimatedIncomeFee"></abc-money>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="订单状态">
                                {{ orderStatusStr }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="隐私号码">
                                {{ privacyPhone }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="备用号码">
                                {{ backupPhone }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="订单号">
                                {{ orderDetail.orderNo }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="下单时间">
                                {{ orderDetail.createdTime | parseTime }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="顾客电话">
                                {{ customerPhone }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="顾客地址">
                                <abc-text tag="div" class="ellipsis" :title="receiverAddress || ''">
                                    {{ receiverAddress || '' }}
                                </abc-text>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="销售人" content-padding="0">
                                <abc-form-item>
                                    <employee-selector
                                        v-model="orderDetail.sellerId"
                                        size="large"
                                        clearable
                                        :disabled="disabled"
                                        :no-icon="disabled"
                                        :employees="employeeList"
                                        data-cy="pharmacy-goods-selector-salesman"
                                    >
                                    </employee-selector>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="药师" content-padding="0">
                                <abc-form-item>
                                    <employee-selector
                                        v-model="orderDetail.pharmacistId"
                                        size="large"
                                        clearable
                                        :disabled="disabled"
                                        :no-icon="disabled"
                                        :employees="pharmacistEmployees"
                                        data-cy="pharmacy-goods-selector-pharmacist"
                                    >
                                    </employee-selector>
                                </abc-form-item>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form>
                </abc-section>
                <abc-section v-if="orderDetail.afterSaleDetailList">
                    <after-sale-table
                        v-for="afterSaleDetail in orderDetail.afterSaleDetailList"
                        :key="afterSaleDetail.id"
                        :after-sale-detail="afterSaleDetail"
                        @refresh="fetchOrderDetail"
                    ></after-sale-table>
                </abc-section>

                <abc-section>
                    <abc-form ref="abcForm">
                        <abc-table
                            :render-config="renderConfig"
                            :data-list="flatFormItems"
                            cell-size="xxxlarge"
                            type="excel"
                            :show-hover-tr-bg="false"
                            :fixed-tr-height="false"
                            class="o2o-goods-out-table"
                            :custom-tr-class="customTrClass"
                            :disabled-item-func="disabledItemFunc"
                        >
                            <template v-if="!disabled" #topHeader>
                                <abc-input
                                    ref="searchInput"
                                    v-model.trim="traceCodeStr"
                                    placeholder="扫描或输入追溯码"
                                    size="medium"
                                    style="width: 728px;"
                                    :max-length="100"
                                    :loading="loading"
                                    loading-position="left"
                                    inputmode="none"
                                    data-cy="trace-code-collect-dialog-search-input"
                                    @focus="handleFocus"
                                    @blur="handleBlur"
                                    @enter="handleEnter"
                                >
                                    <label slot="prepend">
                                        <abc-icon icon="s-b-scan-line"></abc-icon>
                                    </label>
                                    <template #appendInner>
                                        <abc-text v-if="traceCodeStr" theme="gray-light">
                                            敲击回车录入
                                        </abc-text>
                                        <template v-else>
                                            <abc-button
                                                v-if="isSupportDefaultTraceCode"
                                                variant="text"
                                                theme="primary"
                                                size="small"
                                                @click="handleOpenNoCode"
                                            >
                                                发药项目无追溯码？
                                            </abc-button>
                                        </template>
                                    </template>
                                </abc-input>
                            </template>

                            <template #orderGoodsInfo="{ trData }">
                                <table-cell-order :item="trData"></table-cell-order>
                            </template>
                            <template #ABCGoods="{ trData }">
                                <table-cell-goods
                                    :disabled="goodsIsDisabled(trData)"
                                    :item="trData"
                                    :mall-name="orderDetail.mallName"
                                    @open-bind-goods="stopBarcodeDetect"
                                    @close-bind-goods="startBarcodeDetect"
                                ></table-cell-goods>
                            </template>
                            <template #outQuantity="{ trData }">
                                <abc-table-cell style="height: 100%;">
                                    <template v-if="trData.isCancelled">
                                        -
                                    </template>
                                    <abc-popover
                                        v-else
                                        theme="yellow"
                                        placement="top"
                                        trigger="hover"
                                        :disabled="!isShortage(trData)"
                                        style="width: 100%;"
                                    >
                                        <abc-text slot="reference" :theme="getUnitCountTheme(trData)">
                                            {{ trData.unitCount }} {{ trData.unit }}
                                        </abc-text>
                                        <div>
                                            <template v-if="noAssignedStock(trData)">
                                                未分配ABC可售库存 <abc-button v-if="trData.productId" variant="text" @click="handleAllocation(trData)">
                                                    分配库存
                                                </abc-button>
                                            </template>
                                            <template v-else>
                                                库存不足，请补足后出库
                                            </template>
                                        </div>
                                    </abc-popover>
                                </abc-table-cell>
                            </template>
                            <template #outBatches="{ trData }">
                                <table-cell-batches
                                    :item="trData"
                                    :disabled="disabled"
                                    required-batch-infos
                                    @click="handleSelectBatches"
                                ></table-cell-batches>
                            </template>
                            <template #collectionCount="{ trData }">
                                <abc-table-cell style="height: 100%; padding: 0 2px;">
                                    <template v-if="trData.isCancelled || isNullCodeGoods(trData)">
                                        -
                                    </template>
                                    <template v-else-if="showShouldCollect(trData)">
                                        <abc-popover
                                            v-if="trData.traceableCodeList"
                                            theme="yellow"
                                            placement="top"
                                            trigger="hover"
                                            :disabled="!trData._isTransformable"
                                            style="width: 100%;"
                                            width="300px"
                                        >
                                            <abc-flex
                                                slot="reference"
                                                align="center"
                                                justify="space-between"
                                                :style="{
                                                    color: getCountColor(trData),
                                                }"
                                            >
                                                <abc-text style="flex: 1; text-align: right;">
                                                    {{ getCollectCount(trData) }}
                                                </abc-text>
                                                <abc-text style="width: 8px;">
                                                    /
                                                </abc-text>
                                                <abc-text style="flex: 1; text-align: left;">
                                                    {{ trData._maxTraceCodeCount || 1 }}
                                                </abc-text>
                                            </abc-flex>
                                            <div>
                                                <abc-flex vertical>
                                                    <abc-text
                                                        size="mini"
                                                        style="margin-bottom: 6px;"
                                                    >
                                                        医保中心要求，严格按照医保目录规格采集追溯码：
                                                    </abc-text>
                                                    <abc-text theme="gray" size="mini">
                                                        本次出库数量 =
                                                        {{ getUnitCount(trData) }}
                                                        {{ trData.unit }}
                                                        {{
                                                            trData._useLimitPriceTargetUnit ?
                                                                "（该药品为医保目录限价药品）" :
                                                                ""
                                                        }}
                                                    </abc-text>
                                                    <abc-text theme="gray" size="mini">
                                                        {{ displayShebaoSpec(trData) }}
                                                    </abc-text>
                                                    <abc-text theme="gray" size="mini">
                                                        经系统换算，本次应采集
                                                        {{ trData._maxTraceCodeCount }} 个追溯码
                                                    </abc-text>
                                                </abc-flex>
                                            </div>
                                        </abc-popover>
                                    </template>
                                    <template v-else>
                                        {{ getCollectCount(trData) }}
                                    </template>
                                </abc-table-cell>
                            </template>
                            <template #traceCode="{ trData }">
                                <abc-table-cell v-if="trData.isCancelled" style="width: 100%; height: 100%; padding: 0 12px;">
                                    -
                                </abc-table-cell>
                                <table-cell-trace-code-list
                                    v-else
                                    :tr-data="trData"
                                    class="order-out-trace-code-list"
                                    :disabled="disabled"
                                    is-compatible-history-data
                                    @change="handleCodeChange"
                                ></table-cell-trace-code-list>
                            </template>
                            <template #operate="{ trData }">
                                <table-cell-history
                                    v-if="trData.productId && !trData.isCancelled"
                                    :disabled="disabled"
                                    :goods-id="trData.productId"
                                    :pharmacy-no="0"
                                    @add-code="handleAddCode"
                                ></table-cell-history>
                            </template>
                        </abc-table>
                    </abc-form>
                </abc-section>
            </abc-layout>

            <div slot="footer" class="dialog-footer">
                <abc-space v-if="shouldCollectItem" style="font-size: 14px;">
                    <abc-text theme="gray">
                        采集项目
                    </abc-text>
                    <abc-text v-if="outStockStatus === EcOrderOutStatus.OUT_STOCK" theme="gray" bold>
                        {{ collectedItem }}/{{ shouldCollectItem }}
                    </abc-text>
                    <abc-text v-else :theme="collectedItem < shouldCollectItem ? 'warning-light' : 'success-light'" bold>
                        {{ collectedItem }}/{{ shouldCollectItem }}
                    </abc-text>
                    <template v-if="isStrictCount && shouldCollectCount">
                        <abc-text theme="gray">
                            ，采集数量
                        </abc-text>
                        <abc-text v-if="outStockStatus === EcOrderOutStatus.OUT_STOCK" theme="gray" bold>
                            {{ collectedCount }}/{{ shouldCollectCount }}
                        </abc-text>
                        <abc-text v-else :theme="collectedCount < shouldCollectCount || collectCountError ? 'warning-light' : 'success-light'" bold>
                            {{ collectedCount }}/{{ shouldCollectCount }}
                        </abc-text>
                    </template>
                </abc-space>
                <div style="flex: 1;"></div>
                <abc-popover
                    v-if="!disabled"
                    trigger="hover"
                    placement="top-start"
                    width="auto"
                    theme="yellow"
                    :disabled="notSyncSkuGoods.length === 0"
                >
                    <div slot="reference">
                        <abc-button
                            style="min-width: 82px;"
                            :loading="btnLoading"
                            :disabled="notSyncSkuGoods.length"
                            @click="handleClickShip"
                        >
                            确定出库
                        </abc-button>
                    </div>
                    <div>
                        订单中存在未同步商品，正在同步中，预计30秒
                    </div>
                </abc-popover>

                <abc-button v-if="orderDetail.orderTypeFlag & 8" variant="ghost" @click="openPrescriptionDialog">
                    处方登记
                </abc-button>
                <abc-button
                    v-if="!disabled"
                    style="min-width: 82px;"
                    variant="ghost"
                    :loading="btnLoading"
                    @click="handleSaveDraft"
                >
                    保存草稿
                </abc-button>
                <abc-button variant="ghost" @click="handleClickClose">
                    关闭
                </abc-button>
            </div>
        </abc-dialog>
        <dialog-prescription-registration
            v-if="showPrescriptionDialog"
            v-model="showPrescriptionDialog"
            :order-id="orderId"
            :ext-order-id="orderNo"
            :disabled="!isValidMall || isChainAdmin"
            :pharmacist-name="pharmacistName"
        ></dialog-prescription-registration>
    </div>
</template>

<script type="text/babel">
    import BarcodeDetectorV2 from 'MfBase/barcode-detector-v2';
    import EmployeeSelector from 'MfBase/employee-selector';
    import { ROLE_PHARMACY_DOCTOR } from 'MfBase/constants';
    import TraceCode, {
        SceneTypeEnum,
        TraceableCodeTypeEnum,
        TraceCodeScenesEnum,
    } from 'MfBase/trace-code/service';
    import TraceCodeConfirmDialog from 'MfBase/trace-code/dialog-trace-code-confirm';
    import TraceCodeMatchDialog from 'MfBase/trace-code/dialog-trace-code-match';
    import NoTraceCodeCheckDialog from 'MfBase/trace-code/dialog-no-trace-code-check';
    import TableCellHistory from 'MfBase/trace-code/components/table-cell-history';
    import TableCellTraceCodeList from 'MfBase/trace-code/components/table-cell-trace-code-list';
    import TraceCodeWarningDialog from 'MfBase/trace-code/dialog-trace-code-warning';
    import TipsTraceCode from 'MfBase/trace-code/components/tips-trace-code';
    import TableCellOrder from '@/components/o2o-goods-out-table/table-cell-order.vue';
    import TableCellGoods from '@/components/o2o-goods-out-table/table-cell-goods.vue';
    import TableCellBatches from '@/components/o2o-goods-out-table/table-cell-batches.vue';
    import AfterSaleTable from '@/components/o2o-goods-out-table/after-sale-table.vue';
    import DialogPrescriptionRegistration from '@/components/dialog-prescription-registration.vue';
    import DialogBatchesSelector from '@/components/dialog-batches-selector/index.js';

    import {
        OrderStatusTextEnum, OrderLogisticsStatusTextEnum, OrderStatusEnum,
    } from '@/daemon/crawler/provider/meituan/constants.js';
    import {
        createGUID, clone,
    } from '@/utils/index.js';
    import { isEqual } from 'MfBase/lodash';
    import ECOrderAPI from '@/api/order';
    import { EcOrderOutStatus } from '@/utils/constants.js';
    import { mapGetters } from 'vuex';
    import localStorage from 'MfBase/utils/localStorage-handler';
    import DialogAllocationStock from '@/components/dialog-allocation-stock';
    import { OrderCloudDaemonService } from '@/daemon/index.js';
    import { AuthStatus } from '@/daemon/crawler/common/constants';
    import {
        ECTypeText,
        ECTypeEnum,
        EcShopTypeEnum,
    } from '@/utils/constants';

    export default {
        name: 'O2OGoodsOutDialog',
        components: {
            EmployeeSelector,
            TableCellHistory,
            TableCellTraceCodeList,
            TipsTraceCode,
            TableCellOrder,
            TableCellGoods,
            TableCellBatches,
            AfterSaleTable,
            DialogPrescriptionRegistration,
        },
        props: {
            orderId: {
                type: String,
                required: true,
            },
            orderNo: {
                type: String,
                required: true,
            },
            outStockStatus: {
                type: Number,
                default: 0,
            },
            orderStatus: {
                type: Number,
                default: 0,
            },
            onRefresh: {
                type: Function,
            },
            isValidMall: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                EcOrderOutStatus,
                traceCodeStr: '',
                codeQueue: [], // 获取到的追溯码队列
                visible: false,
                btnLoading: false,
                disabledOutBtn: false,
                loading: false,
                searchKeyword: '',

                shouldCollectItem: 0,
                shouldCollectCount: 0,
                collectedItem: 0,
                collectedCount: 0,
                collectCountError: false,
                isStrictCount: false,
                contentLoading: false,
                showPrescriptionDialog: false,
                orderDetail: {

                },
                flatFormItems: [],
                curOutStockStatus: this.outStockStatus,
                curOrderStatus: this.orderStatus,
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'employeeList',
                'userInfo',
                'currentClinic',
            ]),
            pharmacistEmployees() {
                return this.employeeList?.filter((it) => it.roleIds?.includes(ROLE_PHARMACY_DOCTOR)) || [];
            },
            pharmacistName() {
                return this.pharmacistEmployees?.find((it) => it.id === this.orderDetail.pharmacistId)?.name || '';
            },
            disabled() {
                return this.isChainAdmin || this.curOutStockStatus !== EcOrderOutStatus.WAIT || this.curOrderStatus === OrderStatusEnum.canceled || !this.isValidMall;
            },
            isSupportDefaultTraceCode() {
                return this.$abcSocialSecurity.defaultDrugTraceCode
                    ?.isSupportDefaultTraceCode;
            },
            renderConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'orderGoodsInfo',
                            label: '订单商品',
                            style: {
                                flex: '1',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'ABCGoods',
                            label: '绑定的ABC系统商品',
                            style: {
                                flex: '1',
                            },
                        },
                        {
                            key: 'outQuantity',
                            label: '出库数量',
                            style: {
                                flex: 'none',
                                width: '80px',
                            },
                        },
                        {
                            key: 'outBatches',
                            label: '出库批号',
                            style: {
                                flex: 'none',
                                width: '150px',
                            },
                        },
                        {
                            key: 'collectionCount',
                            label: TraceCode.isStrictCount ? '已采/应采' : '采集数量',
                            style: {
                                flex: 'none',
                                width: '84px',
                                textAlign: 'center',
                            },
                        },
                        {
                            key: 'traceCode',
                            label: '追溯码',
                            style: {
                                width: '270px',
                                maxWidth: '270px',
                            },
                        },
                        {
                            key: 'operate',
                            label: '',
                            style: {
                                width: '40px',
                                maxWidth: '40px',
                            },
                        },
                    ],
                };
            },

            receiverInfoStr() {
                const {
                    receiverInfo,
                } = this.orderDetail || {};
                const {
                    receiverNameMask = '',
                    receiverPhoneMask = '',
                } = receiverInfo || {};
                return `${receiverNameMask} ${receiverPhoneMask}`;
            },
            receiverAddress() {
                const {
                    receiverInfo,
                } = this.orderDetail || {};
                const {
                    receiverAddressMask,
                } = receiverInfo || {};
                return `${receiverAddressMask || ''}`;
            },
            ecTypeText() {
                return ECTypeText[this.orderDetail.ecType] || '';
            },

            ecAdditionalInfo() {
                return this.orderDetail.ecAdditionalInfo || {};
            },
            orderDaySeq() {
                return this.ecAdditionalInfo.orderDaySeq || '';
            },
            recipientPhones() {
                return this.ecAdditionalInfo.recipientPhones || [];
            },
            //隐私号码
            privacyPhone() {
                return this.recipientPhones.find((it) => it.name === '隐私号码')?.phoneShow || '';
            },
            //备用号码
            backupPhone() {
                return this.recipientPhones.find((it) => it.name === '备用号码')?.phoneShow || '';
            },
            //顾客电话
            customerPhone() {
                return this.recipientPhones.find((it) => it.name === '顾客电话')?.phoneShow || '';
            },
            orderStatusStr() {
                const {
                    orderStatus,
                    logisticsStatus,
                } = this.orderDetail;
                const orderStatusStr = OrderStatusTextEnum[orderStatus] || '';
                const logisticsStatusStr = OrderLogisticsStatusTextEnum[logisticsStatus] || '';
                return `${orderStatusStr}（${logisticsStatusStr}）`;
            },
            notSyncSkuGoods() {
                return this.flatFormItems.filter((it) => {
                    return it.isSkuNotSync;
                });
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
            showPrescriptionDialog(val) {
                val ? this.stopBarcodeDetect() : this.startBarcodeDetect();
            },
        },
        created() {
            this.initHandler();
        },
        mounted() {
            this.startBarcodeDetect();
        },
        beforeDestroy() {
            this.stopBarcodeDetect();
        },
        methods: {
            async initHandler() {
                this._key = `${this.currentClinic.clinicId}_${this.currentClinic.userId}`;
                this.isStrictCount = TraceCode.isStrictCount;
                this.$abcEventBus.$on('refresh-ec-goods', () => this.fetchOrderDetail(), this);
                await this.fetchOrderDetail();
                await this.createMaxTraceCount();
                // 初始化只生成code上的错误
                this.generateCodeWarnInfo(false);
                this.getCountHandler();
            },
            async fetchOrderDetail(showLoading = true) {
                this.contentLoading = showLoading;
                try {
                    const res = await ECOrderAPI.fetchOrderDetail({
                        orderId: this.orderId,
                        customErrorTips: true,
                    });

                    this.curOutStockStatus = res.outStockStatus;

                    this.flatFormItems = [];
                    res.orderItems.forEach((it) => {
                        const keyId = it.keyId || createGUID();
                        if (it.goodsStockList?.length) {
                            it.goodsStockList.forEach((stock) => {
                                const productInfo = stock.hisGoodsInfo;
                                const dispensingItem = {
                                    id: it.id,
                                    keyId,
                                    clinicId: it.clinicId,
                                    extGoodsImg: it.extGoodsImg,
                                    extGoodsName: it.extGoodsName,
                                    extGoodsCount: it.extGoodsCount,
                                    extGoodsSkuId: it.extGoodsSkuId,
                                    ecGoodsId: it.ecGoodsId,
                                    ecGoodsSkuId: it.ecGoodsSkuId,
                                    ecGoodsSku: it.ecGoodsSku,
                                    isCancelled: it.isCancelled,
                                    productId: stock.hisGoodsId,
                                    extSkuId: it.extSkuId,
                                    productInfo,
                                    useDismounting: stock.useDismounting,
                                    packageCount: stock.packageCount,
                                    pieceCount: stock.pieceCount,
                                    unitCount: stock.useDismounting ? stock.pieceCount : stock.packageCount,
                                    unit: stock.useDismounting ? productInfo?.pieceUnit : productInfo?.packageUnit,
                                    goodsStockId: stock.id,
                                    lockStockId: stock.lockId,
                                    traceableCodeList: stock.stockDetail?.traceableCodeList || [],
                                    batchInfos: stock.stockDetail?.batchInfos || [],
                                };
                                dispensingItem.traceableCodeList = this.initTraceCodeList(dispensingItem);
                                this.flatFormItems.push(dispensingItem);
                            });
                        } else {
                            const dispensingItem = {
                                id: it.id,
                                keyId,
                                clinicId: it.clinicId,
                                extGoodsImg: it.extGoodsImg,
                                extGoodsName: it.extGoodsName,
                                extGoodsCount: it.extGoodsCount,
                                extGoodsSkuId: it.extGoodsSkuId,
                                ecGoodsId: it.ecGoodsId,
                                ecGoodsSkuId: it.ecGoodsSkuId,
                                ecGoodsSku: it.ecGoodsSku,
                                extSkuId: it.extSkuId,
                                isCancelled: it.isCancelled,
                                isSkuNotSync: it.isSkuNotSync,
                                productId: null,
                                productInfo: null,
                                useDismounting: false,
                                packageCount: 0,
                                pieceCount: 0,
                                unitCount: 0,
                                unit: null,
                                goodsStockId: null,
                                lockStockId: null,
                                traceableCodeList: [],
                                batchInfos: [],
                            };
                            this.flatFormItems.push(dispensingItem);
                        }
                    });

                    this._flatFormItemsCache = clone(this.flatFormItems);
                    this.orderDetail = Object.assign({}, {
                        actualAmount: res.actualAmount,
                        orderNo: res.orderNo,
                        createdTime: res.createdTime,
                        ecType: res.ecType,
                        receiverInfo: res.receiverInfo,
                        ecAdditionalInfo: res.ecAdditionalInfo,
                        orderStatus: res.orderStatus,
                        orderTypeFlag: res.orderTypeFlag,
                        logisticsStatus: res.logisticsStatus,
                        afterSaleDetailList: res.afterSaleDetailList,
                        pharmacistId: res.pharmacistId,
                        sellerId: res.sellerId,
                        estimatedIncomeDetail: res.estimatedIncomeDetail,
                        mallName: res.mallName?.replace(`${this.ecTypeText}-`, ''),
                    });


                    if (!this.orderDetail.pharmacistId) {
                        // 若登录账号为药师角色，药师默认值为当前药师；若非药师记忆上次开单药师
                        if (this.userInfo?.roleIds.includes(ROLE_PHARMACY_DOCTOR)) {
                            this.orderDetail.pharmacistId = this.userInfo.id;
                        } else {
                            const res = localStorage.get('order_cloud_last_selected_pharmacist', true) || '';
                            if (res) {
                                this.orderDetail.pharmacistId = res;
                            }
                        }
                        // 药师不在药师列表中，清空药师
                        if (!this.pharmacistEmployees.some((it) => it.id === this.orderDetail.pharmacistId)) {
                            this.orderDetail.pharmacistId = '';
                        }
                    }

                    if (!this.orderDetail.sellerId) {
                        const res = localStorage.get('order_cloud_last_selected_sellerId', true) || '';
                        if (res) {
                            this.orderDetail.sellerId = res;
                        }
                    }

                    this.scrapeProductListBySkuIds();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            async scrapeProductListBySkuIds() {
                if (this.notSyncSkuGoods.length === 0) return;
                const notSyncSkuList = this.notSyncSkuGoods.map((it) => it.extSkuId);
                const crawlerManager = OrderCloudDaemonService.getInstance().getCrawlerManager();
                if (crawlerManager) {
                    const authMallList = await crawlerManager.getAuthMallList();
                    const MTMall = authMallList.find((mall) => mall.shopType === EcShopTypeEnum.O2O && mall.ecType === ECTypeEnum.MT);
                    const meituanService = crawlerManager.getTargetService(MTMall.extMallId);

                    const {
                        authStatus,
                    } = meituanService;
                    if (authStatus !== AuthStatus.AUTHED) {
                        return;
                    }
                    meituanService.scrapeProductListBySkuIds(notSyncSkuList, () => {
                        this.fetchOrderDetail(false);
                    });
                }
            },

            customTrClass(trData) {
                if (trData.isCancelled) {
                    return 'is-disabled';
                }
                return '';
            },
            disabledItemFunc(trData) {
                return trData.isCancelled > 0;
            },
            goodsIsDisabled(trData) {
                return this.disabled || trData.isCancelled > 0;
            },
            noAssignedStock(trData) {
                const {
                    ecGoodsSku,
                } = trData;
                if (!ecGoodsSku) return true;
                if (!ecGoodsSku.relHisGoodsList[0]) return true;
                const {
                    assignedStockPackageCount,
                    assignedStockPieceCount,
                } = ecGoodsSku.relHisGoodsList[0];
                return assignedStockPieceCount === null && assignedStockPackageCount === null;
            },
            isShortage(trData) {
                if (this.disabled) return false;
                const {
                    useDismounting,
                    unitCount,
                    productInfo,
                    ecGoodsSku,
                    lockStockId,
                } = trData;
                // 如果已被锁库，不用判断库存了
                if (lockStockId) return false;
                if (!ecGoodsSku) return true;
                if (!ecGoodsSku.relHisGoodsList[0]) return true;
                const {
                    pieceNum,
                } = productInfo || {};
                const {
                    assignedStockPackageCount: stockPackageCount,
                    assignedStockPieceCount: stockPieceCount,
                } = ecGoodsSku.relHisGoodsList[0];
                const stockPieceNum = pieceNum || 1;
                const stockTotal = stockPackageCount * stockPieceNum + stockPieceCount;
                if (useDismounting) {
                    return stockTotal < unitCount;
                }
                return stockPackageCount < unitCount;
            },
            getUnitCountTheme(trData) {
                if (this.isShortage(trData)) {
                    return 'warning-light';
                }
                return '';
            },
            getCountColor(trData) {
                return TraceCode.needCountWarn(trData) ? 'var(--abc-color-Y2)' : '';
            },
            getCollectCount(trData) {
                return TraceCode.getTraceCodeCollectCount(trData);
            },
            isNullCodeGoods(trData) {
                return TraceCode.isNullCodeGoods(trData.productInfo);
            },
            showShouldCollect(trData) {
                if (!TraceCode.isSupportTraceCodeForceCheckPharmacy()) return false;
                if (!TraceCode.isStrictCount) return false;
                if (!window.$abcSocialSecurity.isOpenSocial) return false;
                return this.supportCollect(trData);
            },
            supportCollect(trData) {
                return TraceCode.supportCollect(trData.productInfo);
            },
            customTrDataCy(item) {
                return `trace-code-collect-panel-tr-${item.name}`;
            },
            displayShebaoSpec(trData) {
                return TraceCode.displayShebaoSpec(trData.productInfo);
            },
            getUnitCount(item) {
                return TraceCode.getUnitCount(item);
            },
            handleOpenNoCode() {
                this._noTraceCodeCheckDialog = new NoTraceCodeCheckDialog({
                    formItems: this.flatFormItems,
                    onConfirm: (checkedList) => {
                        checkedList.forEach((item) => {
                            this.flatFormItems.forEach((it) => {
                                const goodsId = item.productInfo?.id;
                                // 将同一种商品都设置为无码
                                if (goodsId === it.productInfo?.id) {
                                    this.$set(it.productInfo, 'traceableCodeNoInfoList', [{
                                        type: TraceableCodeTypeEnum.NO_CODE,
                                        no: TraceCode.getDefaultNoCodeIdentification(it.productInfo),
                                    }]);
                                    it.traceableCodeList = this.initTraceCodeList(it);
                                }
                            });
                        });
                    },
                    onClose: () => {
                        this._noTraceCodeCheckDialog = null;
                    },
                });
                this._noTraceCodeCheckDialog.generateDialogAsync();
            },

            handleCodeChange() {
                this.generateCodeWarnInfo(false);
                this.getCountHandler();
            },
            getCountHandler() {
                const shouldCollectItems = this.flatFormItems.filter((item) => TraceCode.supportCollect(item.productInfo));
                const collectedItems = this.flatFormItems.filter((item) => item.traceableCodeList?.length);
                this.shouldCollectItem = shouldCollectItems.length;
                this.shouldCollectCount = shouldCollectItems.reduce((acc, item) => acc + (item._maxTraceCodeCount || 1), 0);
                this.collectedItem = collectedItems.length;
                this.collectedCount = collectedItems.reduce((total, item) => {
                    const codeCount = item.traceableCodeList?.reduce((acc, cur) => acc + (+cur.count || 1), 0);
                    return total + (+codeCount || 1);
                }, 0);
                this.collectCountError = shouldCollectItems.some((it) => {
                    const codeCount = it.traceableCodeList?.reduce((acc, cur) => acc + (+cur.count || 1), 0);
                    return codeCount < (it._maxTraceCodeCount || 1);
                });
            },

            async createMaxTraceCount(list) {
                if (!TraceCode.isSupportTraceCodeForceCheckPharmacy()) return;
                const maxTraceCountList = await TraceCode.getMaxTraceCountList({
                    dataList: list || this.flatFormItems,
                    scene: TraceCodeScenesEnum.PHARMACY,
                    getUnitInfo: (item) => {
                        return {
                            ...item,
                            unitCount: TraceCode.getUnitCount(item),
                        };
                    },
                });
                if (maxTraceCountList?.length) {
                    this.flatFormItems.forEach((item) => {
                        const res = maxTraceCountList.find((it) => (it.keyId === item.keyId));
                        if (res) {
                            item._maxTraceCodeCount = res.traceableCodeNum || 1;
                            // 否可以转换 0:否 1:是, 换算程序无法覆盖的，系统不进行校验
                            this.$set(item, '_isTransformable', res.isTransformable);
                            // 是否应用了限价单位
                            this.$set(item, '_useLimitPriceTargetUnit', res.useLimitPriceTargetUnit);
                            // 默认采集数量，按医保大单位开单，但是地区医保要求采“小单位数量”个追溯码，这儿开1盒，规格是 10支/盒，此时新加的追溯码数量就应该是10
                            item._collectCountTransFactor = res.collectCountTransFactor || 1;

                            const {
                                productInfo,
                            } = item;
                            // 是无码商品并且又返回了traceableCodeNum，需要修正数量
                            if (TraceCode.isNoTraceCodeGoods(productInfo)) {
                                const _list = TraceCode.initNoTraceCodeList(item);
                                if (_list && _list.length) {
                                    item.traceableCodeList = _list;
                                }
                            }
                        }
                    });
                }
            },


            isNoTraceCodeGoods(item) {
                return TraceCode.isNoTraceCodeGoods(item.productInfo);
            },
            /**
             * @desc 无码需要初始化且不可改
             * <AUTHOR> Yang
             * @date 2024-09-10 14:01:00
            */
            initTraceCodeList(item) {
                const {
                    productInfo,
                } = item;
                // 是无码商品并且没有录入追溯码的情况下生成
                if (TraceCode.isNoTraceCodeGoods(productInfo) && !item.traceableCodeList?.length) {
                    const res = TraceCode.initNoTraceCodeList(item);
                    if (res && res.length) {
                        return res;
                    }
                }
                return (item.traceableCodeList || []).map((it) => {
                    it.keyId = it.keyId || createGUID();
                    return it;
                });
            },
            startBarcodeDetect() {
                if (this.disabled) return;
                if (this.barcodeDetector) return;
                this.barcodeDetector = BarcodeDetectorV2.getInstance();
                this.barcodeDetector.startDetect(this.handleScanCode, true, (instance) => {
                    this.isScanning = instance.isScanning;
                });
            },
            stopBarcodeDetect() {
                if (!this.barcodeDetector) return;
                this.barcodeDetector.stopDetect(this.handleScanCode);
                this.barcodeDetector = null;
            },
            getItemId(item) {
                return item.id || item.keyId;
            },

            validateOrder(needTips = false) {
                let noGoods = false;
                let goodsIsDisabled = false;
                let shortage = false;
                this.flatFormItems.forEach((item) => {
                    if (!item.productInfo) {
                        noGoods = true;
                    } else if (item.productInfo.v2DisableStatus > 10) {
                        goodsIsDisabled = true;
                    } else if (this.isShortage(item)) {
                        shortage = true;
                    }
                });
                if (needTips) {
                    if (noGoods) {
                        this.$Toast({
                            message: '该订单中存在项目未绑定ABC商品',
                            type: 'error',
                        });
                    }
                }
                return new Promise((resolve) => {
                    this.$refs.abcForm?.validate((valid) => {
                        resolve(noGoods || shortage || goodsIsDisabled || !valid);
                    });
                });
            },

            async handleClickShip() {
                if (await this.validateOrder(true)) return;

                const res = await TraceCode.validate({
                    scene: TraceCodeScenesEnum.PHARMACY,
                    dataList: this.flatFormItems,
                    sceneType: SceneTypeEnum.SELL,
                    getUnitInfo: (item) => {
                        return {
                            ...item,
                            unitCount: TraceCode.getUnitCount(item),
                        };
                    },
                    needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckPharmacy(),
                });
                if (res.flag) {
                    await this.validatePharmaceutical();
                } else {
                    this.generateCodeWarnInfo();
                    this.$confirm({
                        type: 'warn',
                        title: '风险提醒',
                        content: res.errorList.map((it) => {
                            const {
                                count,
                                warnTips,
                            } = it;
                            return `有 ${count} 个商品${warnTips}`;
                        }),
                        confirmText: '去修改',
                        cancelText: '仍要提交',
                        disabledKeyboard: true,
                        showClose: false,
                        onCancel: this.validatePharmaceutical,
                    });

                    this.scrollTarget(res.errorList[0]);
                }
            },
            scrollTarget(item) {
                if (item.keyId) {
                    this.$nextTick(() => {
                        this.$refs.abcTable?.$el.querySelector(`.abc-table-tr[data-id="${item.keyId}"]`).scrollIntoView({
                            behavior: 'smooth',
                        });
                    });
                }
            },
            async validatePharmaceutical() {
                try {
                    // 海南对接药监追溯码查询
                    if (await TraceCode.getPharmaceuticalTraceCodeQueryConfig()) {
                        const data = await TraceCode.fetchPharmaceuticalTraceCodeDetails({
                            dataList: this.flatFormItems,
                            beforeHook: () => {
                                this._loading = this.$Loading({
                                    text: '正在核验追溯码',
                                    customClass: 'print-loading-wrapper',
                                });
                            },
                            afterHook: () => {
                                this._loading?.close();
                            },
                            errorHook: (error) => {
                                this.$Toast({
                                    type: 'error',
                                    message: error?.message ?? '查询失败',
                                });
                            },
                        });
                        if (data?.length) {
                            new TraceCodeWarningDialog({
                                value: true,
                                traceCodeDetails: data,
                                onConfirm: this.handleShipOrder,
                            }).generateDialogAsync();
                            return;
                        }
                    }
                } catch (e) {
                    console.error(e);
                }

                this.handleShipOrder();
            },
            generateCodeWarnInfo(needItemWarn = true) {
                this.flatFormItems.forEach((item) => {
                    item.composeChildren?.forEach((child) => {
                        if (needItemWarn || child.warnInfo) {
                            this.$set(child, 'warnInfo', TraceCode.validateDataItem({
                                dataItem: child,
                                sceneType: SceneTypeEnum.SELL,
                            }));
                        }
                        child.traceableCodeList.forEach((it) => {
                            it.warnInfo = TraceCode.validateTraceCode({
                                dataItem: child,
                                traceCodeObj: it,
                                goodsInfo: child.productInfo,
                                sceneType: SceneTypeEnum.SELL,
                            });
                        });
                    });

                    if (needItemWarn || item.warnInfo) {
                        this.$set(item, 'warnInfo', TraceCode.validateDataItem({
                            dataItem: item,
                            sceneType: SceneTypeEnum.SELL,
                        }));
                    }
                    item.traceableCodeList.forEach((it) => {
                        it.warnInfo = TraceCode.validateTraceCode({
                            dataItem: item,
                            traceCodeObj: it,
                            goodsInfo: item.productInfo,
                            sceneType: SceneTypeEnum.SELL,
                        });
                    });
                });
            },
            handleFocus() {
                this.stopBarcodeDetect();
            },
            handleBlur() {
                this.startBarcodeDetect();
            },
            handleEnter(e) {
                if (!e.target.value) return;
                this.codeQueue.push(e.target.value);
                this.traceCodeStr = '';
                e.target.value = '';
                this.triggerHandler();
            },
            handleScanCode(e, code) {
                if (e.target === this.$refs.searchInput?.$el) {
                    return;
                }
                this.codeQueue.push(code);
                this.triggerHandler();
            },
            async triggerHandler() {
                // 在处理中不再重复执行
                if (this.loading) return;
                const code = this.codeQueue.shift();
                await this.handleTraceCode(code);
                if (this.codeQueue.length) {
                    this.triggerHandler();
                }
            },
            async handleTraceCode(code) {
                code = code.replace(/\s+/g, '');
                if (!TraceCode.isTraceableCode(code)) {
                    this.$Toast({
                        type: 'error',
                        message: TraceCode.hasInterceptTraceCodeFormatLength() ? '无法采集追溯码（医保要求药品追溯码必须为20位或30位，且不应存在汉字或其他符号）' : '未识别到有效的追溯码，请检查扫描或输入是否正确！',
                    });
                    this.traceCodeStr = '';
                    return;
                }

                this.loading = true;
                try {
                    /**
                     * @desc 优先在本地发药项目中招一下是否有直接用，不再发请求
                     * <AUTHOR> Yang
                     * @date 2024-11-01 11:07:38
                     */
                    let traceCodeInfo = await TraceCode.findCodeInfoInLocal(this.flatFormItems, code);
                    if (!traceCodeInfo) {
                        traceCodeInfo = await TraceCode.fetchByCode(code);
                    }
                    const {
                        goodsInfo,
                        traceableCodeNoInfo,
                        traceCode,
                    } = traceCodeInfo;

                    // 没有解析出药品标识码，都走选择绑定发药项目逻辑
                    if (!traceableCodeNoInfo?.drugIdentificationCode) {
                        this.handleMatchTraceCode(traceCodeInfo);
                        return;
                    }

                    const res = TraceCode.findCanAddTraceCodeItem({
                        goodsInfo,
                        traceableCodeNoInfo,
                        dataList: this.flatFormItems,
                    });
                    if (res) {
                        this.handleAddTraceCode(res, traceCodeInfo);
                        this.generateCodeWarnInfo(false);
                        this.scrollTarget(res);
                    } else {

                        if (!goodsInfo) {
                            this.handleMatchTraceCode(traceCodeInfo);
                            return;
                        }

                        if (this._traceCodeConfirmDialog) return;
                        this._traceCodeConfirmDialog = new TraceCodeConfirmDialog({
                            title: '追溯码关联的商品不在发药项目中',
                            traceableCodeNoInfo,
                            traceCode,
                            goodsInfo,
                            onConfirm: (params) => {
                                const {
                                    clearBound = false,
                                } = params || {};
                                // 解绑后清空goodsInfo
                                if (clearBound) {
                                    delete traceCodeInfo.goodsInfo;
                                }
                                this.handleMatchTraceCode(traceCodeInfo);
                            },
                            onClose: () => {
                                this._traceCodeConfirmDialog = null;
                            },
                        });
                        this._traceCodeConfirmDialog.generateDialogAsync();
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            handleMatchTraceCode(traceCodeInfo) {
                const {
                    goodsInfo,
                    traceableCodeNoInfo,
                    traceCode,
                } = traceCodeInfo;
                this._TraceCodeMatchDialog = new TraceCodeMatchDialog({
                    formItems: this.flatFormItems,
                    traceableCodeNoInfo,
                    traceCode,
                    goodsInfo,
                    onConfirm: (res) => {
                        this.confirmMatchHandler(res, traceCodeInfo);
                    },
                    onClose: () => {
                        this._TraceCodeMatchDialog = null;
                    },
                });
                this._TraceCodeMatchDialog.generateDialogAsync();
            },

            async confirmMatchHandler(res, traceCodeInfo) {
                // [特殊无码商品]绑码后需要更新应采数量, 因为初始化会过滤掉他
                const _obj = this.flatFormItems.find((it) => it.keyId === res.keyId);
                if (_obj && TraceCode.isNullCodeGoods(_obj.productInfo)) {
                    await this.createMaxTraceCount([res]);
                }
                // 某些情况下需要暂存绑定规则
                this.flatFormItems.forEach((it) => {
                    if (it.productInfo && it.productInfo.id === res.productInfo.id) {
                        this.$set(it.productInfo, 'traceableCodeNoInfoList', res.productInfo.traceableCodeNoInfoList);
                    }
                    if (it.keyId === res.keyId) {
                        // 非无码商品匹配后需要清空无码标记
                        if (!TraceCode.isNoTraceCodeGoods(it.productInfo)) {
                            it.traceableCodeList = it.traceableCodeList.filter((item) => {
                                return item.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE;
                            });
                        }
                        this.handleAddTraceCode(it, traceCodeInfo);
                    }
                });
                this.generateCodeWarnInfo(false);
                this.getCountHandler();
                this.scrollTarget(res);
            },

            handleAddTraceCode(item, traceCodeInfo) {
                if (item.isCancelled) return;
                const {
                    traceableCodeNoInfo,
                    traceableCodeList,
                    traceCode,
                } = traceCodeInfo;

                // 重复扫码只用加数量
                const repeatCode = item.traceableCodeList.find((it) => it.no === traceCode);
                let keyId = null;
                const count = TraceCode.getTraceCodeCount(item);
                if (repeatCode) {
                    /**
                     * @desc 后台说如果是1就不会返回count
                     * <AUTHOR> Yang
                     * @date 2024-11-07 17:32:47
                    */
                    if (!repeatCode.count) {
                        this.$set(repeatCode, 'count', 1);
                    }
                    repeatCode.count = +repeatCode.count + +count;
                    keyId = repeatCode.keyId;
                } else {
                    keyId = createGUID();
                    item.traceableCodeList.push({
                        traceableCodeNoInfo,
                        traceableCodeList,
                        no: traceCode,
                        keyId,
                        count,
                    });
                    /**
                     * @desc 剩余应采数量<=0，默认数量就是1，需要在已采集并且采集数量大于1的最末位码减去1
                     * <AUTHOR> Yang
                     * @date 2024-11-18 16:20:34
                     */
                    const {
                        _maxTraceCodeCount,
                        _collectCountTransFactor,
                    } = item;
                    if (_collectCountTransFactor > 1 && item.traceableCodeList.length > 0) {
                        const totalCount = TraceCode.getTraceCodeCollectCount(item);
                        if (totalCount - _maxTraceCodeCount > 0) {
                            let idx = -1;
                            item.traceableCodeList.forEach((it, index) => {
                                if (it.count > 1) {
                                    idx = index;
                                }
                            });
                            if (idx > -1) {
                                item.traceableCodeList[idx].count--;
                            }
                        }
                    }
                    this.calcEcOrderBatches(item);
                }
                this.getCountHandler();
                this.$nextTick(() => {
                    const $code = this.$el.querySelector(`.trace-code-list-item-${keyId}`);
                    if ($code) {
                        $code.classList.add('highlight');
                        this._timer = setTimeout(() => {
                            $code.classList.remove('highlight');
                        }, 1500);
                    }
                });
            },

            handleAddCode(codeInfo) {
                this.handleTraceCode(codeInfo.traceCode);
            },

            openPrescriptionDialog() {
                this.showPrescriptionDialog = true;
            },

            handleSelectBatches(item) {
                this._batcherDialog = new DialogBatchesSelector({
                    chargeItem: item,
                    onConfirm: (data) => {
                        Object.assign(item, {
                            isExpectedBatch: data.isExpectedBatch,
                            batchInfos: data.batchInfos,
                        });
                        this.calcEcOrderBatches(item);
                    },
                });
                this._batcherDialog.generateDialog();
            },

            getOrderItemStruct(item) {
                const {
                    id,
                    goodsStockId,
                    productId,
                    traceableCodeList,
                    batchInfos,
                    pieceCount,
                    packageCount,
                    lockStockId,
                    isExpectedBatch = 0,
                } = item;
                return {
                    id,
                    goodsStockList: [{
                        id: goodsStockId,
                        hisGoodsId: productId,
                        pieceCount,
                        packageCount,
                        lockId: lockStockId,
                        stockDetail: {
                            traceableCodeList,
                            batchInfos,
                            isExpectedBatch,
                        },
                    }],
                };
            },

            getPostData() {
                return {
                    sellerId: this.orderDetail.sellerId,
                    pharmacistId: this.orderDetail.pharmacistId,
                    orderItems: this.flatFormItems.map((item) => {
                        return this.getOrderItemStruct(item);
                    }),
                };
            },

            async calcEcOrderBatches(item) {
                const res = await ECOrderAPI.calculateEcOrder(this.orderId, {
                    orderItems: [this.getOrderItemStruct(item)],
                });
                if (!res.orderItems) return;
                const orderItem = res.orderItems[0];
                if (!orderItem) return;
                item.batchInfos = orderItem.goodsStockList[0]?.stockDetail?.batchInfos;
            },

            async handleSaveDraft() {
                this.btnLoading = true;
                try {
                    await ECOrderAPI.saveOrder(this.orderId, this.getPostData());
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                    this.handleClose();
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: e.message,
                    });
                } finally {
                    this.btnLoading = false;
                }
            },

            async handleShipOrder() {
                this.btnLoading = true;
                try {
                    await ECOrderAPI.shipOrderById(this.orderId, this.getPostData());
                    this.$Toast({
                        type: 'success',
                        message: '出库成功',
                    });
                    this.onRefresh && this.onRefresh();
                    this.handleClose();
                    localStorage.set('order_cloud_last_selected_pharmacist', this.orderDetail.pharmacistId);
                    localStorage.set('order_cloud_last_selected_sellerId', this.orderDetail.sellerId);
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: e.message,
                    });
                } finally {
                    this.btnLoading = false;
                }
            },

            handleClickClose() {
                if (this.disabled) {
                    this.handleClose();
                    return;
                }
                const compareKey = [
                    'traceableCodeList',
                    'batchInfos',
                ];
                let orderEqual = true;
                this.flatFormItems.forEach((item) => {
                    this._flatFormItemsCache.forEach((cacheItem) => {
                        orderEqual = compareKey.every((key) => {
                            return isEqual(item[key], cacheItem[key]);
                        });
                    });
                });
                if (orderEqual) {
                    this.handleClose();
                } else {
                    this.$confirm({
                        title: '提示',
                        content: '是否需要保存为草稿',
                        confirmText: '保存',
                        cancelText: '不保存',
                        showClose: false,
                        onConfirm: () => {
                            this.handleSaveDraft();
                        },
                        onCancel: () => {
                            this.handleClose();
                        },
                    });
                }
            },
            handleAllocation(item) {
                new DialogAllocationStock({
                    ecType: ECTypeEnum.MT,
                    clinicId: item.clinicId,
                    hisGoodsId: item.productId,
                    onChange: () => {
                        this.fetchOrderDetail();
                    },
                }).generateDialogAsync();
            },
            handleClose() {
                this.visible = false;
            },
            destroyElement() {
                this.$destroy();
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
    .o2o-goods-out-dialog {
        .order-out-trace-code-list {
            .abc-input__inner {
                height: 26px !important;
                border-color: var(--abc-color-P7) !important;
            }
        }
    }
</style>

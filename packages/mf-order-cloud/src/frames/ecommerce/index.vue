<template>
    <abc-card class="content-card-wrapper ecommerce-index-wrapper pharmacy__ec-goods-table-section" :border="false" radius-size="none">
        <order-cloud-warn v-if="showOrderWarn"></order-cloud-warn>
        <abc-tabs-v2
            v-model="currentTab"
            :option="tabOptions"
            size="large"
            :item-min-width="64"
            :border-style="{ borderBottom: 'none' }"
            class="content-card-tabs"
            @change="handleTabChange"
        ></abc-tabs-v2>
        <abc-divider margin="none" style="width: auto; min-width: auto; margin: 0 20px;"></abc-divider>
        <router-view></router-view>
    </abc-card>
</template>

<script>
    import {
        PharmacyOrderCloudRouterNameKeys, OrderCloudModuleId,
    } from '../../core/routes';
    import OrderCloudWarn from '../../components/order-cloud-warn.vue';
    import { mapGetters } from 'vuex';
    import ECAuthAPI from '@/api/auth';
    import {
        ECTypeEnum,
    } from '../../utils/constants';

    export default {
        name: 'OrderCloudEcommerce',
        components: {
            OrderCloudWarn,
        },
        inject: {
            $abcPage: {
                default: {},
            },
            eCOrderStockOutStat: {
                default: () => ({
                    stockOutExceptionCount: 0,
                }),
            },
        },
        data() {
            return {
                currentTab: '',
                isInDesktop: false,
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'isChainAdmin',
            ]),
            moduleIds() {
                return (this.userInfo && this.userInfo.moduleIds) || '';
            },
            moduleArr() {
                if (!this.moduleIds) {
                    return [];
                }
                return this.moduleIds.split(',');
            },
            hasEcommerceAllModule() {
                if (!this.moduleIds) {
                    return false;
                }
                return this.moduleIds === '0' || this.moduleArr.includes(OrderCloudModuleId.main) || this.moduleArr.includes(OrderCloudModuleId.ecommerce);
            },
            tabOptions() {
                const options = [];

                // 订单选项
                if (this.hasEcommerceAllModule || this.moduleArr.includes(OrderCloudModuleId.ecommerceOrder)) {
                    options.push({
                        label: '订单管理',
                        value: PharmacyOrderCloudRouterNameKeys.ecommerceOrder,
                        moduleId: OrderCloudModuleId.ecommerceOrder,
                    });
                }

                // 出库记录选项
                if (this.hasEcommerceAllModule || this.moduleArr.includes(OrderCloudModuleId.ecommerceOutRecord)) {
                    options.push({
                        label: '出库记录',
                        value: PharmacyOrderCloudRouterNameKeys.ecommerceOutRecord,
                        moduleId: OrderCloudModuleId.ecommerceOutRecord,
                        maxNoticeNumber: 99,
                        noticeNumber: this.eCOrderStockOutStat.stockOutExceptionCount,
                    });
                }

                // 商品选项
                if (this.hasEcommerceAllModule || this.moduleArr.includes(OrderCloudModuleId.ecommerceEcGoods)) {
                    options.push({
                        label: '商品管理',
                        value: PharmacyOrderCloudRouterNameKeys.ecommerceEcGoods,
                        moduleId: OrderCloudModuleId.ecommerceEcGoods,
                        separation: true,
                    });
                }

                // 营收统计选项
                if (this.hasEcommerceAllModule || this.moduleArr.includes(OrderCloudModuleId.ecommerceEcStatistics)) {
                    options.push({
                        label: '营收统计',
                        value: PharmacyOrderCloudRouterNameKeys.ecommerceEcStatistics,
                        moduleId: OrderCloudModuleId.ecommerceEcStatistics,
                    });
                }

                // 订单统计选项
                if (this.hasEcommerceAllModule) {
                    options.push({
                        label: '订单统计',
                        value: PharmacyOrderCloudRouterNameKeys.ecommerceEcOrderStatistics,
                        moduleId: OrderCloudModuleId.ecommerceEcOrderStatistics,
                    });
                }

                return options;
            },
            showOrderWarn() {
                return this.isInDesktop && !this.isChainAdmin;
            },
        },
        created() {
            this.isInDesktop = window.electronFlag;
            // 根据当前路由初始化选中的选项卡
            this.initRouteAndTab();
            this.pddFingerprint();
        },
        methods: {
            async pddFingerprint() {
                const res = await ECAuthAPI.fetchBindAuthList({
                    offset: 0,
                    limit: 1000,
                });
                this.bindMallList = (res?.rows || []).filter((item) => item.ecType === ECTypeEnum.PDD);
                this.$abcPage.pddFingerprint({
                    ecMallIds: this.bindMallList.map((item) => item.ecMallId),
                });
            },
            initRouteAndTab() {
                const currentRouteInTabs = this.tabOptions.some((tab) => tab.value === this.$route.name);

                // 如果当前路由不在 tabOptions 中，则跳转到第一个可用的选项卡
                if (!currentRouteInTabs) {
                    const availableTab = this.tabOptions[0];

                    if (availableTab) {
                        // 跳转到第一个可用的选项卡
                        if (this.$route.name !== availableTab.value) {
                            this.$router.push({
                                name: availableTab.value,
                            });
                        }
                        this.currentTab = availableTab.value;
                    }
                } else {
                    // 如果当前路由在 tabOptions 中，则更新当前选中的选项卡
                    this.currentTab = this.$route.name;
                }
            },
            handleTabChange(name) {
                if (this.$route.name !== name) {
                    this.$router.push({
                        name,
                    });
                }
            },
        },
    };
</script>
<style lang="scss">
    .ecommerce-index-wrapper {
        position: relative;

        .abc-card-body {
            display: flex;
            flex-direction: column;
        }

        .order-cloud-warn-wrapper {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 1;
        }
    }
</style>
